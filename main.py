# main.py

from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager
import threading
import time

from api.api import router
from api.sse import router as sse_router
from api.gpt_prompts_api import router as gpt_prompts_router
from api.content_validator_api import router as content_validator_router
from api.pyqs_admin_api import router as pyqs_admin_router
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from utils.logging_config import setup_logging
from utils.log_disabler import disable_all_logs
from auth.login_api import router as login_router
from starlette.middleware.sessions import SessionMiddleware
from auth.middleware import CacheControlMiddleware
from video_module.router import router as video_router
from question_paper_aggregator_module.router import router as question_paper_router
from db_config.env_loader import env_loader

from config import SHOW_LOGS
import logging

logger = logging.getLogger(__name__)

# Initialize the logging configuration
setup_logging()

# Disable all logs if SHOW_LOGS is False
if not SHOW_LOGS:
    disable_all_logs()

# Test database connections at startup
logger.info("Testing database connections at startup...")
logger.info("=" * 50)

# Import all schema constants
from db_config.db import AUTH_SCHEMA, SHOP_SCHEMA, COMM_SCHEMA, LOG_SCHEMA, CONTENT_SCHEMA

# Function to test and log connection to a specific schema
def test_schema_connection(schema_name):
    """Test connection to a specific schema and log the result with host information"""
    # Get database configuration to show the host being used
    config = env_loader.get_database_config(schema_name)
    host = config.get("host")

    logger.critical(f"Testing connection to schema: {schema_name} on host: {host}")

    # Test connection
    if env_loader.test_connection(schema_name):
        logger.critical(f"✅ Successfully connected to {schema_name} schema on host {host}")
        return True
    else:
        logger.critical(f"❌ Failed to connect to {schema_name} schema on host {host}")
        return False

# Test connection to AUTH_SCHEMA first (required for application to function)
auth_connected = test_schema_connection(AUTH_SCHEMA)
if not auth_connected:
    logger.critical(f"Failed to connect to authentication schema: {AUTH_SCHEMA}")
    logger.critical("Application may not function correctly without database connection")

# Test connections to other schemas
for schema in [SHOP_SCHEMA, COMM_SCHEMA, LOG_SCHEMA, CONTENT_SCHEMA]:
    test_schema_connection(schema)

logger.critical("Database connection tests completed")
logger.info("=" * 50)
# Test database connections at startup
logger.info("Testing database connections at startup...")
logger.info("=" * 50)

# Import all schema constants
from db_config.db import AUTH_SCHEMA, SHOP_SCHEMA, COMM_SCHEMA, LOG_SCHEMA, CONTENT_SCHEMA

# Function to test and log connection to a specific schema
def test_schema_connection(schema_name):
    """Test connection to a specific schema and log the result with host information"""
    # Get database configuration to show the host being used
    config = env_loader.get_database_config(schema_name)
    host = config.get("host")

    logger.info(f"Testing connection to schema: {schema_name} on host: {host}")

    # Test connection
    if env_loader.test_connection(schema_name):
        logger.info(f"✅ Successfully connected to {schema_name} schema on host {host}")
        return True
    else:
        logger.error(f"❌ Failed to connect to {schema_name} schema on host {host}")
        return False

# Test connection to AUTH_SCHEMA first (required for application to function)
auth_connected = test_schema_connection(AUTH_SCHEMA)
if not auth_connected:
    logger.error(f"Failed to connect to authentication schema: {AUTH_SCHEMA}")
    logger.error("Application may not function correctly without database connection")

# Test connections to other schemas
for schema in [SHOP_SCHEMA, COMM_SCHEMA, LOG_SCHEMA, CONTENT_SCHEMA]:
    test_schema_connection(schema)

logger.info("Database connection tests completed")
logger.info("=" * 50)

# Define lifespan context manager for application startup/shutdown events
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: run before the application starts accepting requests
    logger.critical("Application starting up")
    start_background_task_cleanup()
    yield
    # Shutdown: run when the application is shutting down
    logger.critical("Application shutting down")

# Initialize the FastAPI application with lifespan context manager
app = FastAPI(lifespan=lifespan)

# Add session middleware
app.add_middleware(
    SessionMiddleware,
    secret_key="your-secret-key-here",  # In production, use a secure random key
    max_age=3600,  # Session expiration time in seconds (1 hour)
    same_site="lax",  # Cookie same-site policy
    https_only=False  # Set to True in production with HTTPS
)

# Add cache control middleware to prevent back navigation
app.add_middleware(CacheControlMiddleware)

# Authentication dependencies are now in auth/dependencies.py

# Mount static files from /web/static at /static
app.mount("/static", StaticFiles(directory="web/static"), name="static")  # Mounting static assets

# Mount static files for video module
app.mount("/video-static", StaticFiles(directory="video_module/static"), name="video-static")

# Mount static files for question paper aggregator module
app.mount("/question-paper-static", StaticFiles(directory="question_paper_aggregator_module/static"), name="question-paper-static")

# Initialize the HTML template engine
templates = Jinja2Templates(directory="web/templates")

# Add built-in functions to Jinja2 template globals
templates.env.globals['max'] = max
templates.env.globals['min'] = min

# Include the API router
app.include_router(router)

# Include the SSE router
app.include_router(sse_router, prefix="/api")

# Include the Login router
app.include_router(login_router)

# Include the Video router
app.include_router(video_router)

# Include the Question Paper Aggregator router
app.include_router(question_paper_router)

# Include the GPT Prompts router
app.include_router(gpt_prompts_router)

# Include the Content Validator router
app.include_router(content_validator_router, prefix="/api/validator")

# Include the PYQs Admin router
app.include_router(pyqs_admin_router)

# Background task to clean up old tasks
def start_background_task_cleanup():
    """Start a background thread to periodically clean up old tasks"""
    from utils.progress_tracker import progress_tracker
    from services.request_tracker_service import RequestTrackerService

    def cleanup_worker():
        """Worker function to clean up old tasks every 12 hours"""
        request_tracker = RequestTrackerService()

        while True:
            try:
                logger.info("Running scheduled task cleanup")
                # Clean up tasks older than 24 hours (86400 seconds)
                progress_tracker.clean_old_tasks(max_age_seconds=86400)

                # Clean up old request tracker tasks (older than 24 hours)
                cleaned_count = request_tracker.clean_old_tasks(max_age_hours=24)
                logger.info(f"Cleaned up {cleaned_count} old request tracker tasks")

                # Sleep for 12 hours (43200 seconds)
                time.sleep(43200)
            except Exception as e:
                logger.error(f"Error in task cleanup worker: {e}")
                # Sleep for 5 minutes before retrying
                time.sleep(300)

    # Start the cleanup thread
    cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
    cleanup_thread.start()
    logger.critical("Started background task cleanup thread")


@app.get("/api/")
def read_root():
    """
    Root endpoint that returns a welcome message.
    It accepts a GET request and does not require any parameters.

    Returns:
        dict: A dictionary with a welcome message.
    """
    return {"message": "Welcome to the IBookGPT"}


# Main entry point of the application
if __name__ == "__main__":
    import uvicorn

    # Use SHOW_LOGS setting from earlier import

    # Set Uvicorn log level based on SHOW_LOGS setting
    log_level_config = "info" if SHOW_LOGS else "critical"
    log_access_config = SHOW_LOGS

    # Run the application using Uvicorn with automatic reloading
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True, log_level=log_level_config, access_log=log_access_config)
