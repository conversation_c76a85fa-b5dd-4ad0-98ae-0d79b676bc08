from fastapi import APIRouter, Depends, Request, HTTPException
from auth.dependencies import require_login
from auth.rbac import require_roles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
from pathlib import Path

from db_config.pyqs_admin_db import get_exam_by_id
from .api.api import api_router as question_paper_api_router, cleanup_all_sessions

# Create router
router = APIRouter(prefix="/question-paper-aggregator", tags=["question-paper-aggregator"])

# Clean up any existing sessions on module initialization
cleanup_all_sessions()

# Templates
templates_path = Path(__file__).parent / "templates"
templates = Jinja2Templates(directory=str(templates_path))

# Add built-in functions to Jinja2 template globals
templates.env.globals['max'] = max
templates.env.globals['min'] = min

# Also use the main application templates
main_templates_path = Path(__file__).parent.parent / "web" / "templates"
templates.env.loader.searchpath.append(str(main_templates_path))

@router.get("/search/{exam_id}", response_class=HTMLResponse)
async def search_papers_page(
    exam_id: int,
    request: Request,
    login_check=Depends(require_login),
    role_check=Depends(require_roles(["ROLE_GPT_MANAGER"]))
):
    """
    Question paper search page
    """
    if login_check:
        return login_check

    # Get exam details
    exam = get_exam_by_id(exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    return templates.TemplateResponse("search_papers.html", {
        "request": request,
        "exam": exam
    })

# Include the API router
router.include_router(question_paper_api_router)
